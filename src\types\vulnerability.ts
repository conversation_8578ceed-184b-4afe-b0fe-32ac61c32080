export interface Vulnerability {
  id: string;
  title: string;
  description: string;
  impact: string;
  recommendation: string;
  references: Reference[];
  severity: SeverityLevel;
  cvssScore?: number;
  cveId?: string;
  publishedDate?: string;
  lastModified?: string;
  affectedSystems?: string[];
  exploitAvailable?: boolean;
  tags?: string[];
}

export interface Reference {
  url: string;
  title: string;
  type: 'advisory' | 'exploit' | 'patch' | 'vendor' | 'other';
}

export type SeverityLevel = 'critical' | 'high' | 'medium' | 'low' | 'informational';

export interface SearchFilters {
  severity?: SeverityLevel[];
  hasExploit?: boolean;
  dateRange?: {
    start: string;
    end: string;
  };
  cvssRange?: {
    min: number;
    max: number;
  };
  tags?: string[];
}

export interface SearchResult {
  vulnerabilities: Vulnerability[];
  totalCount: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export interface VulnerabilitySource {
  name: string;
  url: string;
  lastUpdated: string;
  isActive: boolean;
}

// API Response interfaces
export interface CVEResponse {
  CVE_data_meta: {
    ID: string;
    ASSIGNER: string;
  };
  description: {
    description_data: Array<{
      lang: string;
      value: string;
    }>;
  };
  impact?: {
    baseMetricV3?: {
      cvssV3: {
        baseScore: number;
        baseSeverity: string;
      };
    };
  };
  references: {
    reference_data: Array<{
      url: string;
      name: string;
      refsource: string;
    }>;
  };
  publishedDate?: string;
  lastModifiedDate?: string;
}

export interface NVDResponse {
  result: {
    CVE_Items: Array<{
      cve: CVEResponse;
      configurations?: any;
      impact?: any;
      publishedDate: string;
      lastModifiedDate: string;
    }>;
    CVE_data_numberOfCVEs: number;
    CVE_data_timestamp: string;
  };
}

// Penetration Testing Report interfaces
export interface PenTestFinding {
  id: string;
  vulnerability: Vulnerability;
  evidence: string[];
  exploitSteps: string[];
  businessImpact: string;
  technicalImpact: string;
  likelihood: 'very-low' | 'low' | 'medium' | 'high' | 'very-high';
  riskRating: 'informational' | 'low' | 'medium' | 'high' | 'critical';
  affectedAssets: string[];
  screenshots?: string[];
  proofOfConcept?: string;
}

export interface PenTestReport {
  id: string;
  title: string;
  client: string;
  testDate: string;
  tester: string;
  executiveSummary: string;
  scope: string[];
  methodology: string;
  findings: PenTestFinding[];
  recommendations: string[];
  appendices?: string[];
  createdAt: string;
  updatedAt: string;
}
