import axios from 'axios';
import { Vulnerability, SearchResult, SearchFilters, SeverityLevel, CVEResponse, NVDResponse } from '../types/vulnerability';

class VulnerabilityService {
  private readonly baseUrls = {
    nvd: 'https://services.nvd.nist.gov/rest/json/cves/2.0',
    cveDetails: 'https://cvedetails.com/json-feed.php',
    exploitDb: 'https://www.exploit-db.com/api/v1',
    mitre: 'https://cve.mitre.org/cgi-bin/cvekey.cgi',
    vulndb: 'https://vulndb.cyberriskanalytics.com/api/v1',
    circl: 'https://cve.circl.lu/api',
    opencve: 'https://www.opencve.io/api',
    github: 'https://api.github.com/advisories',
    osv: 'https://api.osv.dev/v1',
    snyk: 'https://snyk.io/api/v1',
    vulners: 'https://vulners.com/api/v3'
  };

  private readonly corsProxy = 'https://api.allorigins.win/raw?url=';
  private cache = new Map<string, { data: any; timestamp: number }>();
  private readonly cacheTimeout = 5 * 60 * 1000; // 5 minutes

  private readonly mockVulnerabilities: Vulnerability[] = [
    {
      id: 'CVE-2024-0001',
      title: 'SQL Injection in Web Application Authentication',
      description: 'It was observed that the web application\'s authentication mechanism is vulnerable to SQL injection attacks. The vulnerability exists in the login form where user input is not properly sanitized before being processed by the database query. An attacker can manipulate the SQL query by injecting malicious SQL code through the username or password fields, potentially bypassing authentication controls and gaining unauthorized access to the application. This vulnerability affects the main authentication endpoint and could lead to complete system compromise if exploited successfully.',
      impact: 'An attacker can bypass authentication mechanisms and gain unauthorized access to the application with administrative privileges. This could lead to complete system compromise, data theft, unauthorized data modification, and potential lateral movement within the network infrastructure.',
      recommendation: 'Implement parameterized queries or prepared statements to prevent SQL injection. Validate and sanitize all user inputs. Use least privilege principles for database connections. Implement proper error handling to avoid information disclosure.',
      references: [
        { url: 'https://owasp.org/www-community/attacks/SQL_Injection', title: 'OWASP SQL Injection', type: 'advisory' },
        { url: 'https://cwe.mitre.org/data/definitions/89.html', title: 'CWE-89: SQL Injection', type: 'advisory' }
      ],
      severity: 'critical',
      cvssScore: 9.8,
      cveId: 'CVE-2024-0001',
      publishedDate: '2024-01-15',
      lastModified: '2024-01-20',
      affectedSystems: ['Web Application', 'Database Server'],
      exploitAvailable: true,
      tags: ['sql-injection', 'authentication', 'web-application']
    },
    {
      id: 'CVE-2024-0002',
      title: 'Cross-Site Scripting (XSS) in User Profile Page',
      description: 'It was observed that the user profile page contains a stored cross-site scripting vulnerability. The application fails to properly encode user-supplied data when displaying profile information, allowing malicious scripts to be stored and executed in other users\' browsers. The vulnerability exists in the "About Me" section where users can input HTML content that gets rendered without proper sanitization. This creates a persistent XSS condition that affects all users who view the compromised profile.',
      impact: 'An attacker can execute malicious JavaScript code in the context of other users\' browsers, potentially stealing session cookies, performing actions on behalf of users, or redirecting users to malicious websites. This could lead to account takeover and data theft.',
      recommendation: 'Implement proper output encoding for all user-supplied data. Use Content Security Policy (CSP) headers. Validate and sanitize user inputs on both client and server side. Consider using a web application firewall (WAF).',
      references: [
        { url: 'https://owasp.org/www-community/attacks/xss/', title: 'OWASP XSS Prevention', type: 'advisory' },
        { url: 'https://cwe.mitre.org/data/definitions/79.html', title: 'CWE-79: Cross-site Scripting', type: 'advisory' }
      ],
      severity: 'high',
      cvssScore: 7.2,
      cveId: 'CVE-2024-0002',
      publishedDate: '2024-01-10',
      lastModified: '2024-01-18',
      affectedSystems: ['Web Application'],
      exploitAvailable: false,
      tags: ['xss', 'web-application', 'stored-xss']
    },
    {
      id: 'CVE-2024-0003',
      title: 'Insecure Direct Object Reference in File Download',
      description: 'It was observed that the file download functionality contains an insecure direct object reference vulnerability. The application uses predictable file identifiers in the download URL without proper authorization checks. Users can manipulate the file ID parameter to access files belonging to other users or system files. The vulnerability exists in the /download endpoint where the file parameter is directly used to construct file paths without validation.',
      impact: 'An attacker can access sensitive files belonging to other users or system configuration files. This could lead to information disclosure, privacy violations, and potential system compromise if critical system files are accessible.',
      recommendation: 'Implement proper access controls and authorization checks for file access. Use indirect object references or UUIDs instead of predictable identifiers. Validate file paths and restrict access to authorized directories only.',
      references: [
        { url: 'https://owasp.org/www-community/attacks/Insecure_Direct_Object_Reference', title: 'OWASP IDOR', type: 'advisory' },
        { url: 'https://cwe.mitre.org/data/definitions/639.html', title: 'CWE-639: Insecure Direct Object Reference', type: 'advisory' }
      ],
      severity: 'medium',
      cvssScore: 6.5,
      cveId: 'CVE-2024-0003',
      publishedDate: '2024-01-08',
      lastModified: '2024-01-15',
      affectedSystems: ['Web Application', 'File Server'],
      exploitAvailable: false,
      tags: ['idor', 'file-access', 'authorization']
    }
  ];

  // Cache management
  private isValidCache(key: string): boolean {
    const cached = this.cache.get(key);
    if (!cached) return false;
    return Date.now() - cached.timestamp < this.cacheTimeout;
  }

  private getFromCache(key: string): any {
    const cached = this.cache.get(key);
    return cached ? cached.data : null;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  // Real API integration methods
  async searchNVDDatabase(query: string, resultsPerPage = 20): Promise<Vulnerability[]> {
    try {
      const cacheKey = `nvd_${query}_${resultsPerPage}`;
      if (this.isValidCache(cacheKey)) {
        return this.getFromCache(cacheKey);
      }

      const params = new URLSearchParams({
        keywordSearch: query,
        resultsPerPage: resultsPerPage.toString(),
        startIndex: '0'
      });

      const response = await axios.get(`${this.baseUrls.nvd}?${params}`, {
        timeout: 10000,
        headers: {
          'User-Agent': 'SecureVault-Pro/1.0'
        }
      });

      const vulnerabilities = this.parseNVDResponse(response.data);
      this.setCache(cacheKey, vulnerabilities);
      return vulnerabilities;
    } catch (error) {
      console.error('NVD API Error:', error);
      return [];
    }
  }

  async searchCVEById(cveId: string): Promise<Vulnerability | null> {
    try {
      const cacheKey = `cve_${cveId}`;
      if (this.isValidCache(cacheKey)) {
        return this.getFromCache(cacheKey);
      }

      // Try multiple sources for CVE data
      const sources = [
        () => this.fetchFromNVD(cveId),
        () => this.fetchFromCIRCL(cveId),
        () => this.fetchFromOpenCVE(cveId)
      ];

      for (const source of sources) {
        try {
          const result = await source();
          if (result) {
            this.setCache(cacheKey, result);
            return result;
          }
        } catch (error) {
          console.warn('Source failed, trying next:', error.message);
        }
      }

      return null;
    } catch (error) {
      console.error('CVE lookup error:', error);
      return null;
    }
  }

  async searchExploitDB(query: string): Promise<any[]> {
    try {
      const cacheKey = `exploitdb_${query}`;
      if (this.isValidCache(cacheKey)) {
        return this.getFromCache(cacheKey);
      }

      // ExploitDB doesn't have a public API, so we'll use a proxy or scraping approach
      const searchUrl = `https://www.exploit-db.com/search?q=${encodeURIComponent(query)}`;

      // Note: In a real implementation, you'd need to handle CORS and potentially use a backend
      const response = await axios.get(`${this.corsProxy}${encodeURIComponent(searchUrl)}`, {
        timeout: 15000
      });

      const exploits = this.parseExploitDBResponse(response.data);
      this.setCache(cacheKey, exploits);
      return exploits;
    } catch (error) {
      console.error('ExploitDB search error:', error);
      return [];
    }
  }

  async searchVulnerabilities(query: string, filters?: SearchFilters, page = 1, pageSize = 10): Promise<SearchResult> {
    try {
      let allVulnerabilities: Vulnerability[] = [];

      // Search multiple sources in parallel
      const searchPromises = [
        this.searchNVDDatabase(query, 50),
        this.searchGitHubAdvisories(query),
        this.searchOSVDatabase(query),
        // Fallback to mock data if APIs fail
        Promise.resolve(this.searchMockData(query))
      ];

      const results = await Promise.allSettled(searchPromises);

      results.forEach((result, index) => {
        if (result.status === 'fulfilled' && result.value) {
          allVulnerabilities = allVulnerabilities.concat(result.value);
        } else {
          console.warn(`Search source ${index} failed:`, result.status === 'rejected' ? result.reason : 'No data');
        }
      });

      // Remove duplicates based on CVE ID
      const uniqueVulns = this.removeDuplicates(allVulnerabilities);

      // Apply filters
      let filteredVulns = this.applyFilters(uniqueVulns, filters);

      // Sort by severity and date
      filteredVulns = this.sortVulnerabilities(filteredVulns);

      // Paginate results
      const startIndex = (page - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedVulns = filteredVulns.slice(startIndex, endIndex);

      return {
        vulnerabilities: paginatedVulns,
        totalCount: filteredVulns.length,
        page,
        pageSize,
        hasMore: endIndex < filteredVulns.length
      };
    } catch (error) {
      console.error('Error searching vulnerabilities:', error);
      // Fallback to mock data
      return this.searchMockData(query, filters, page, pageSize);
    }
  }

  async getVulnerabilityById(id: string): Promise<Vulnerability | null> {
    try {
      const vulnerability = this.mockVulnerabilities.find(vuln => vuln.id === id || vuln.cveId === id);
      return vulnerability || null;
    } catch (error) {
      console.error('Error fetching vulnerability:', error);
      throw new Error('Failed to fetch vulnerability');
    }
  }

  async searchNVD(cveId: string): Promise<Vulnerability | null> {
    try {
      // In a real implementation, you would call the NVD API
      // const response = await axios.get(`${this.baseUrls.nvd}?cveId=${cveId}`);
      // For now, return mock data if it matches
      return this.getVulnerabilityById(cveId);
    } catch (error) {
      console.error('Error searching NVD:', error);
      return null;
    }
  }

  private mapSeverity(cvssScore: number): SeverityLevel {
    if (cvssScore >= 9.0) return 'critical';
    if (cvssScore >= 7.0) return 'high';
    if (cvssScore >= 4.0) return 'medium';
    if (cvssScore >= 0.1) return 'low';
    return 'info';
  }





  async getLatestVulnerabilities(limit = 10): Promise<Vulnerability[]> {
    try {
      // Sort by published date and return the most recent
      const sorted = [...this.mockVulnerabilities].sort((a, b) => 
        new Date(b.publishedDate || '').getTime() - new Date(a.publishedDate || '').getTime()
      );
      return sorted.slice(0, limit);
    } catch (error) {
      console.error('Error fetching latest vulnerabilities:', error);
      throw new Error('Failed to fetch latest vulnerabilities');
    }
  }

  // Helper methods for API data processing
  private async fetchFromNVD(cveId: string): Promise<Vulnerability | null> {
    const response = await axios.get(`${this.baseUrls.nvd}?cveId=${cveId}`);
    return this.parseNVDSingleResponse(response.data);
  }

  private async fetchFromCIRCL(cveId: string): Promise<Vulnerability | null> {
    const response = await axios.get(`${this.baseUrls.circl}/cve/${cveId}`);
    return this.parseCIRCLResponse(response.data);
  }

  private async fetchFromOpenCVE(cveId: string): Promise<Vulnerability | null> {
    const response = await axios.get(`${this.baseUrls.opencve}/cve/${cveId}`);
    return this.parseOpenCVEResponse(response.data);
  }

  private async searchGitHubAdvisories(query: string): Promise<Vulnerability[]> {
    try {
      const response = await axios.get(`${this.baseUrls.github}?q=${encodeURIComponent(query)}`, {
        headers: {
          'Accept': 'application/vnd.github.v3+json',
          'User-Agent': 'SecureVault-Pro/1.0'
        }
      });
      return this.parseGitHubAdvisories(response.data);
    } catch (error) {
      console.error('GitHub Advisories error:', error);
      return [];
    }
  }

  private async searchOSVDatabase(query: string): Promise<Vulnerability[]> {
    try {
      const response = await axios.post(`${this.baseUrls.osv}/query`, {
        query: query,
        page_token: ''
      });
      return this.parseOSVResponse(response.data);
    } catch (error) {
      console.error('OSV Database error:', error);
      return [];
    }
  }

  private parseNVDResponse(data: any): Vulnerability[] {
    if (!data.vulnerabilities) return [];

    return data.vulnerabilities.map((item: any) => {
      const cve = item.cve;
      const metrics = cve.metrics?.cvssMetricV31?.[0] || cve.metrics?.cvssMetricV30?.[0];

      return {
        id: cve.id,
        title: this.generateTitle(cve),
        description: this.generateDescription(cve.descriptions?.[0]?.value || 'No description available'),
        impact: this.generateImpact(metrics?.cvssData?.baseSeverity || 'unknown'),
        recommendation: this.generateRecommendation(cve),
        references: this.parseReferences(cve.references || []),
        severity: this.mapCVSSSeverity(metrics?.cvssData?.baseSeverity),
        cvssScore: metrics?.cvssData?.baseScore,
        cveId: cve.id,
        publishedDate: cve.published?.split('T')[0],
        lastModified: cve.lastModified?.split('T')[0],
        affectedSystems: this.extractAffectedSystems(cve),
        exploitAvailable: this.checkExploitAvailability(cve),
        tags: this.generateTags(cve)
      };
    });
  }

  private parseNVDSingleResponse(data: any): Vulnerability | null {
    if (!data.vulnerabilities?.[0]) return null;
    return this.parseNVDResponse(data)[0];
  }

  private parseCIRCLResponse(data: any): Vulnerability | null {
    if (!data) return null;

    return {
      id: data.id,
      title: data.summary || `Vulnerability in ${data.id}`,
      description: this.generateDescription(data.summary || 'No description available'),
      impact: this.generateImpact(data.cvss || 'unknown'),
      recommendation: this.generateRecommendation(data),
      references: data.references?.map((ref: string) => ({
        url: ref,
        title: 'Reference',
        type: 'advisory' as const
      })) || [],
      severity: this.mapCVSSSeverity(data.cvss),
      cvssScore: parseFloat(data.cvss) || undefined,
      cveId: data.id,
      publishedDate: data.Published?.split('T')[0],
      lastModified: data.Modified?.split('T')[0],
      affectedSystems: data.vulnerable_product || [],
      exploitAvailable: false,
      tags: this.generateTagsFromText(data.summary || '')
    };
  }

  private parseGitHubAdvisories(data: any): Vulnerability[] {
    if (!data || !Array.isArray(data)) return [];

    return data.map((advisory: any) => ({
      id: advisory.ghsa_id,
      title: advisory.summary,
      description: this.generateDescription(advisory.description || advisory.summary),
      impact: this.generateImpact(advisory.severity),
      recommendation: this.generateRecommendation(advisory),
      references: advisory.references?.map((ref: any) => ({
        url: ref.url,
        title: ref.url,
        type: 'advisory' as const
      })) || [],
      severity: this.mapGitHubSeverity(advisory.severity),
      cvssScore: advisory.cvss?.score,
      cveId: advisory.cve_id,
      publishedDate: advisory.published_at?.split('T')[0],
      lastModified: advisory.updated_at?.split('T')[0],
      affectedSystems: advisory.vulnerabilities?.map((v: any) => v.package?.name) || [],
      exploitAvailable: false,
      tags: ['github-advisory', ...(advisory.vulnerabilities?.map((v: any) => v.package?.ecosystem) || [])]
    }));
  }

  private parseOSVResponse(data: any): Vulnerability[] {
    if (!data.vulns) return [];

    return data.vulns.map((vuln: any) => ({
      id: vuln.id,
      title: vuln.summary || `Vulnerability ${vuln.id}`,
      description: this.generateDescription(vuln.details || vuln.summary || 'No description available'),
      impact: this.generateImpact('medium'), // OSV doesn't always provide severity
      recommendation: this.generateRecommendation(vuln),
      references: vuln.references?.map((ref: any) => ({
        url: ref.url,
        title: ref.type || 'Reference',
        type: 'advisory' as const
      })) || [],
      severity: 'medium' as SeverityLevel,
      cveId: vuln.aliases?.find((alias: string) => alias.startsWith('CVE-')),
      publishedDate: vuln.published?.split('T')[0],
      lastModified: vuln.modified?.split('T')[0],
      affectedSystems: vuln.affected?.map((a: any) => a.package?.name) || [],
      exploitAvailable: false,
      tags: ['osv', ...(vuln.affected?.map((a: any) => a.package?.ecosystem) || [])]
    }));
  }

  private parseExploitDBResponse(html: string): any[] {
    // This would require HTML parsing - simplified for demo
    // In a real implementation, you'd parse the HTML to extract exploit information
    return [];
  }

  private removeDuplicates(vulnerabilities: Vulnerability[]): Vulnerability[] {
    const seen = new Set<string>();
    return vulnerabilities.filter(vuln => {
      const key = vuln.cveId || vuln.id;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  private applyFilters(vulnerabilities: Vulnerability[], filters?: SearchFilters): Vulnerability[] {
    if (!filters) return vulnerabilities;

    let filtered = vulnerabilities;

    if (filters.severity?.length) {
      filtered = filtered.filter(vuln => filters.severity!.includes(vuln.severity));
    }

    if (filters.hasExploit !== undefined) {
      filtered = filtered.filter(vuln => vuln.exploitAvailable === filters.hasExploit);
    }

    if (filters.cvssRange) {
      filtered = filtered.filter(vuln => {
        if (!vuln.cvssScore) return false;
        return vuln.cvssScore >= (filters.cvssRange!.min || 0) &&
               vuln.cvssScore <= (filters.cvssRange!.max || 10);
      });
    }

    if (filters.dateRange) {
      filtered = filtered.filter(vuln => {
        if (!vuln.publishedDate) return false;
        const pubDate = new Date(vuln.publishedDate);
        const startDate = new Date(filters.dateRange!.start);
        const endDate = new Date(filters.dateRange!.end);
        return pubDate >= startDate && pubDate <= endDate;
      });
    }

    return filtered;
  }

  private sortVulnerabilities(vulnerabilities: Vulnerability[]): Vulnerability[] {
    return vulnerabilities.sort((a, b) => {
      // Sort by severity first
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1, info: 0 };
      const severityDiff = (severityOrder[b.severity] || 0) - (severityOrder[a.severity] || 0);
      if (severityDiff !== 0) return severityDiff;

      // Then by CVSS score
      const cvssA = a.cvssScore || 0;
      const cvssB = b.cvssScore || 0;
      if (cvssB !== cvssA) return cvssB - cvssA;

      // Finally by date
      const dateA = new Date(a.publishedDate || '').getTime();
      const dateB = new Date(b.publishedDate || '').getTime();
      return dateB - dateA;
    });
  }

  async getVulnerabilityStats() {
    try {
      // Get real-time stats from multiple sources
      const [nvdStats, githubStats] = await Promise.allSettled([
        this.getNVDStats(),
        this.getGitHubStats()
      ]);

      // Combine with cached data
      const recentSearches = Array.from(this.cache.values())
        .filter(item => Array.isArray(item.data))
        .flatMap(item => item.data);

      const stats = {
        total: recentSearches.length,
        critical: recentSearches.filter((v: Vulnerability) => v.severity === 'critical').length,
        high: recentSearches.filter((v: Vulnerability) => v.severity === 'high').length,
        medium: recentSearches.filter((v: Vulnerability) => v.severity === 'medium').length,
        low: recentSearches.filter((v: Vulnerability) => v.severity === 'low').length,
        withExploits: recentSearches.filter((v: Vulnerability) => v.exploitAvailable).length,
        lastUpdated: new Date().toISOString()
      };

      return stats;
    } catch (error) {
      console.error('Error fetching vulnerability stats:', error);
      // Fallback to mock data stats
      return {
        total: this.mockVulnerabilities.length,
        critical: this.mockVulnerabilities.filter(v => v.severity === 'critical').length,
        high: this.mockVulnerabilities.filter(v => v.severity === 'high').length,
        medium: this.mockVulnerabilities.filter(v => v.severity === 'medium').length,
        low: this.mockVulnerabilities.filter(v => v.severity === 'low').length,
        withExploits: this.mockVulnerabilities.filter(v => v.exploitAvailable).length,
        lastUpdated: new Date().toISOString()
      };
    }
  }

  private async getNVDStats(): Promise<any> {
    // Get recent CVE statistics from NVD
    const response = await axios.get(`${this.baseUrls.nvd}?pubStartDate=${new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]}&pubEndDate=${new Date().toISOString().split('T')[0]}`);
    return response.data;
  }

  private async getGitHubStats(): Promise<any> {
    // Get recent GitHub security advisories
    const response = await axios.get(`${this.baseUrls.github}?per_page=100`, {
      headers: { 'Accept': 'application/vnd.github.v3+json' }
    });
    return response.data;
  }

  // Fallback to mock data search
  private searchMockData(query: string, filters?: SearchFilters, page = 1, pageSize = 10): SearchResult {
    let filteredVulns = this.mockVulnerabilities;

    if (query) {
      const searchTerm = query.toLowerCase();
      filteredVulns = filteredVulns.filter(vuln =>
        vuln.title.toLowerCase().includes(searchTerm) ||
        vuln.description.toLowerCase().includes(searchTerm) ||
        vuln.cveId?.toLowerCase().includes(searchTerm) ||
        vuln.tags?.some(tag => tag.toLowerCase().includes(searchTerm))
      );
    }

    filteredVulns = this.applyFilters(filteredVulns, filters);

    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const paginatedVulns = filteredVulns.slice(startIndex, endIndex);

    return {
      vulnerabilities: paginatedVulns,
      totalCount: filteredVulns.length,
      page,
      pageSize,
      hasMore: endIndex < filteredVulns.length
    };
  }

  // Utility methods for generating realistic vulnerability content
  private generateTitle(cve: any): string {
    const descriptions = cve.descriptions || [];
    const englishDesc = descriptions.find((d: any) => d.lang === 'en')?.value || descriptions[0]?.value || '';

    if (englishDesc.length > 100) {
      return englishDesc.substring(0, 97) + '...';
    }

    return englishDesc || `Vulnerability ${cve.id}`;
  }

  private generateDescription(rawDescription: string): string {
    if (rawDescription.toLowerCase().startsWith('it was observed that')) {
      return rawDescription;
    }

    const templates = [
      `It was observed that the application contains a security vulnerability where ${rawDescription.toLowerCase()}. This vulnerability allows unauthorized access to sensitive functionality and could potentially compromise the security of the entire system. The issue stems from improper input validation and insufficient security controls implemented in the affected component. Further analysis revealed that the vulnerability can be exploited remotely without authentication, making it particularly dangerous for internet-facing applications.`,

      `It was observed that a critical security flaw exists in the system where ${rawDescription.toLowerCase()}. The vulnerability manifests when user-supplied input is processed without adequate sanitization or validation. This security weakness could allow malicious actors to manipulate application behavior and gain unauthorized access to restricted resources. The affected component fails to implement proper security boundaries, creating a significant risk to data confidentiality and system integrity.`,

      `It was observed that the target application is vulnerable to security exploitation through ${rawDescription.toLowerCase()}. This vulnerability occurs due to insufficient security controls and improper handling of user input within the application logic. The security flaw can be triggered by crafting specific requests that bypass existing validation mechanisms. Analysis indicates that successful exploitation could lead to complete system compromise and unauthorized data access.`
    ];

    return templates[Math.floor(Math.random() * templates.length)];
  }

  private generateImpact(severity: string): string {
    const severityLevel = severity.toLowerCase();

    const impactTemplates = {
      critical: [
        'An attacker can gain complete control over the affected system and execute arbitrary commands with elevated privileges. This could result in total system compromise, unauthorized access to all sensitive data, and the ability to pivot to other systems within the network infrastructure.',

        'An attacker can bypass all authentication mechanisms and gain administrative access to the application. This enables complete data exfiltration, system manipulation, and potential deployment of persistent backdoors for long-term access.',

        'An attacker can exploit this vulnerability to achieve remote code execution with system-level privileges. This allows for complete system takeover, installation of malware, and use of the compromised system as a launching point for further attacks.'
      ],
      high: [
        'An attacker can gain unauthorized access to sensitive user data and potentially escalate privileges within the application. This could lead to data theft, account compromise, and unauthorized modification of critical system settings.',

        'An attacker can manipulate application functionality to bypass security controls and access restricted resources. This may result in data corruption, service disruption, and compromise of user accounts.',

        'An attacker can exploit this vulnerability to gain elevated access within the application context. This enables unauthorized data access, privilege escalation, and potential lateral movement within the system.'
      ],
      medium: [
        'An attacker can access limited sensitive information or cause minor disruption to application functionality. This could result in information disclosure, temporary service degradation, or limited unauthorized access.',

        'An attacker can manipulate certain application features to gain unauthorized access to specific data or functionality. The impact is contained but could affect user privacy and application integrity.',

        'An attacker can exploit this vulnerability to cause information leakage or minor security bypasses. While not immediately critical, this could be chained with other vulnerabilities for greater impact.'
      ],
      low: [
        'An attacker can obtain limited information about the system or cause minor inconvenience to users. The direct impact is minimal but could provide reconnaissance information for further attacks.',

        'An attacker can trigger minor application misbehavior or access non-sensitive information. The security impact is limited and requires specific conditions to exploit.',

        'An attacker can cause minor information disclosure or application errors. While the immediate risk is low, this vulnerability could be used as part of a larger attack chain.'
      ]
    };

    const templates = impactTemplates[severityLevel as keyof typeof impactTemplates] || impactTemplates.medium;
    return templates[Math.floor(Math.random() * templates.length)];
  }

  private generateRecommendation(vulnData: any): string {
    const recommendations = [
      'Implement proper input validation and sanitization for all user-supplied data. Use parameterized queries or prepared statements to prevent injection attacks. Apply the principle of least privilege and implement proper access controls. Regularly update and patch all system components.',

      'Deploy comprehensive input validation mechanisms and implement proper output encoding. Use secure coding practices and conduct regular security code reviews. Implement proper error handling to prevent information disclosure. Consider implementing a Web Application Firewall (WAF) for additional protection.',

      'Apply security patches immediately and implement proper authentication and authorization controls. Use secure communication protocols and implement proper session management. Conduct regular security assessments and penetration testing to identify similar vulnerabilities.',

      'Implement proper security controls including input validation, output encoding, and access controls. Use security frameworks and libraries that provide built-in protection against common vulnerabilities. Establish a regular patch management process and security monitoring.',

      'Update to the latest version of the affected software and implement proper security configurations. Use defense-in-depth strategies including network segmentation and monitoring. Implement proper logging and alerting mechanisms to detect potential exploitation attempts.'
    ];

    return recommendations[Math.floor(Math.random() * recommendations.length)];
  }

  private parseReferences(references: any[]): Array<{url: string; title: string; type: 'advisory' | 'exploit' | 'patch' | 'vendor'}> {
    return references.map(ref => ({
      url: ref.url || ref,
      title: ref.name || ref.title || 'Reference',
      type: this.determineReferenceType(ref.url || ref)
    }));
  }

  private determineReferenceType(url: string): 'advisory' | 'exploit' | 'patch' | 'vendor' {
    const urlLower = url.toLowerCase();
    if (urlLower.includes('exploit') || urlLower.includes('poc')) return 'exploit';
    if (urlLower.includes('patch') || urlLower.includes('update')) return 'patch';
    if (urlLower.includes('vendor') || urlLower.includes('security')) return 'vendor';
    return 'advisory';
  }

  private mapCVSSSeverity(cvssString: string): SeverityLevel {
    if (!cvssString) return 'medium';
    const severity = cvssString.toLowerCase();
    if (severity.includes('critical')) return 'critical';
    if (severity.includes('high')) return 'high';
    if (severity.includes('medium')) return 'medium';
    if (severity.includes('low')) return 'low';
    return 'medium';
  }

  private mapGitHubSeverity(severity: string): SeverityLevel {
    if (!severity) return 'medium';
    const sev = severity.toLowerCase();
    if (sev === 'critical') return 'critical';
    if (sev === 'high') return 'high';
    if (sev === 'moderate') return 'medium';
    if (sev === 'low') return 'low';
    return 'medium';
  }

  private extractAffectedSystems(cve: any): string[] {
    const configurations = cve.configurations?.nodes || [];
    const systems: string[] = [];

    configurations.forEach((node: any) => {
      if (node.cpeMatch) {
        node.cpeMatch.forEach((match: any) => {
          if (match.criteria) {
            const parts = match.criteria.split(':');
            if (parts.length > 4) {
              systems.push(`${parts[3]} ${parts[4]}`);
            }
          }
        });
      }
    });

    return [...new Set(systems)].slice(0, 5); // Limit to 5 unique systems
  }

  private checkExploitAvailability(cve: any): boolean {
    // Check references for exploit indicators
    const references = cve.references || [];
    return references.some((ref: any) => {
      const url = (ref.url || '').toLowerCase();
      return url.includes('exploit') || url.includes('poc') || url.includes('metasploit');
    });
  }

  private generateTags(cve: any): string[] {
    const tags: string[] = [];

    // Add severity tag
    const metrics = cve.metrics?.cvssMetricV31?.[0] || cve.metrics?.cvssMetricV30?.[0];
    if (metrics?.cvssData?.baseSeverity) {
      tags.push(metrics.cvssData.baseSeverity.toLowerCase());
    }

    // Add CWE tags
    const weaknesses = cve.weaknesses || [];
    weaknesses.forEach((weakness: any) => {
      weakness.description?.forEach((desc: any) => {
        if (desc.value?.startsWith('CWE-')) {
          tags.push(desc.value.toLowerCase());
        }
      });
    });

    // Add technology tags based on affected systems
    const systems = this.extractAffectedSystems(cve);
    systems.forEach(system => {
      const parts = system.toLowerCase().split(' ');
      tags.push(...parts.filter(part => part.length > 2));
    });

    return [...new Set(tags)].slice(0, 8); // Limit to 8 unique tags
  }

  private generateTagsFromText(text: string): string[] {
    const commonTags = ['web', 'network', 'database', 'authentication', 'authorization', 'injection', 'xss', 'csrf'];
    const textLower = text.toLowerCase();
    return commonTags.filter(tag => textLower.includes(tag));
  }
}

export const vulnerabilityService = new VulnerabilityService();
