import React from 'react';
import { Vulnerability } from '../types/vulnerability';
import { Shield, AlertTriangle, Info, ExternalLink, Calendar, Tag } from 'lucide-react';

interface VulnerabilityCardProps {
  vulnerability: Vulnerability;
  onClick?: () => void;
}

const VulnerabilityCard: React.FC<VulnerabilityCardProps> = ({ vulnerability, onClick }) => {
  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Shield className="w-5 h-5 text-red-600" />;
      case 'high':
        return <AlertTriangle className="w-5 h-5 text-orange-600" />;
      case 'medium':
        return <AlertTriangle className="w-5 h-5 text-yellow-600" />;
      case 'low':
        return <Info className="w-5 h-5 text-blue-600" />;
      default:
        return <Info className="w-5 h-5 text-gray-600" />;
    }
  };

  const getSeverityClass = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'severity-critical';
      case 'high':
        return 'severity-high';
      case 'medium':
        return 'severity-medium';
      case 'low':
        return 'severity-low';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div
      className="vulnerability-card cursor-pointer group card-hover fade-in"
      onClick={onClick}
    >
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-center space-x-3">
          {getSeverityIcon(vulnerability.severity)}
          <div>
            <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 group-hover:text-primary-600 transition-colors">
              {vulnerability.title}
            </h3>
            {vulnerability.cveId && (
              <p className="text-sm text-slate-600 dark:text-slate-400 font-mono">
                {vulnerability.cveId}
              </p>
            )}
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getSeverityClass(vulnerability.severity)}`}>
            {vulnerability.severity.toUpperCase()}
          </span>
          {vulnerability.cvssScore && (
            <span className="px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-700 dark:text-slate-300 rounded text-xs font-mono">
              CVSS: {vulnerability.cvssScore}
            </span>
          )}
        </div>
      </div>

      {/* Description */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Description:</h4>
        <p className="text-slate-600 dark:text-slate-400 text-sm leading-relaxed">
          {truncateText(vulnerability.description, 200)}
        </p>
      </div>

      {/* Impact */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Impact:</h4>
        <p className="text-slate-600 dark:text-slate-400 text-sm leading-relaxed">
          {truncateText(vulnerability.impact, 150)}
        </p>
      </div>

      {/* Recommendation */}
      <div className="mb-4">
        <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Recommendation:</h4>
        <p className="text-slate-600 dark:text-slate-400 text-sm leading-relaxed">
          {truncateText(vulnerability.recommendation, 150)}
        </p>
      </div>

      {/* References */}
      {vulnerability.references && vulnerability.references.length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">References:</h4>
          <div className="space-y-1">
            {vulnerability.references.slice(0, 3).map((ref, index) => (
              <a
                key={index}
                href={ref.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 text-primary-600 hover:text-primary-700 text-sm transition-colors"
                onClick={(e) => e.stopPropagation()}
              >
                <ExternalLink className="w-3 h-3" />
                <span className="truncate">{ref.title}</span>
              </a>
            ))}
            {vulnerability.references.length > 3 && (
              <p className="text-xs text-slate-500">
                +{vulnerability.references.length - 3} more references
              </p>
            )}
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="flex items-center justify-between pt-4 border-t border-slate-200 dark:border-slate-700">
        <div className="flex items-center space-x-4 text-xs text-slate-500">
          {vulnerability.publishedDate && (
            <div className="flex items-center space-x-1">
              <Calendar className="w-3 h-3" />
              <span>{new Date(vulnerability.publishedDate).toLocaleDateString()}</span>
            </div>
          )}
          {vulnerability.exploitAvailable && (
            <span className="px-2 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium">
              Exploit Available
            </span>
          )}
        </div>
        
        {vulnerability.tags && vulnerability.tags.length > 0 && (
          <div className="flex items-center space-x-1">
            <Tag className="w-3 h-3 text-slate-400" />
            <div className="flex space-x-1">
              {vulnerability.tags.slice(0, 2).map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400 rounded text-xs"
                >
                  {tag}
                </span>
              ))}
              {vulnerability.tags.length > 2 && (
                <span className="text-xs text-slate-500">
                  +{vulnerability.tags.length - 2}
                </span>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default VulnerabilityCard;
