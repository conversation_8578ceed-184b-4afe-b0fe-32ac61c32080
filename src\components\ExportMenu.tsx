import React, { useState } from 'react';
import { Download, FileText, Table, Code, X } from 'lucide-react';
import { Vulnerability } from '../types/vulnerability';

interface ExportMenuProps {
  vulnerabilities: Vulnerability[];
  onClose: () => void;
}

const ExportMenu: React.FC<ExportMenuProps> = ({ vulnerabilities, onClose }) => {
  const [selectedFormat, setSelectedFormat] = useState<'json' | 'csv' | 'markdown' | 'xml'>('json');

  const exportData = () => {
    let content = '';
    let filename = '';
    let mimeType = '';

    switch (selectedFormat) {
      case 'json':
        content = JSON.stringify(vulnerabilities, null, 2);
        filename = `vulnerabilities-${new Date().toISOString().split('T')[0]}.json`;
        mimeType = 'application/json';
        break;
      
      case 'csv':
        const csvHeaders = ['ID', 'Title', 'Severity', 'CVSS Score', 'CVE ID', 'Published Date', 'Description', 'Impact', 'Recommendation'];
        const csvRows = vulnerabilities.map(vuln => [
          vuln.id,
          `"${vuln.title.replace(/"/g, '""')}"`,
          vuln.severity,
          vuln.cvssScore || '',
          vuln.cveId || '',
          vuln.publishedDate || '',
          `"${vuln.description.replace(/"/g, '""')}"`,
          `"${vuln.impact.replace(/"/g, '""')}"`,
          `"${vuln.recommendation.replace(/"/g, '""')}"`
        ]);
        content = [csvHeaders.join(','), ...csvRows.map(row => row.join(','))].join('\n');
        filename = `vulnerabilities-${new Date().toISOString().split('T')[0]}.csv`;
        mimeType = 'text/csv';
        break;
      
      case 'markdown':
        content = generateMarkdownReport(vulnerabilities);
        filename = `vulnerabilities-report-${new Date().toISOString().split('T')[0]}.md`;
        mimeType = 'text/markdown';
        break;
      
      case 'xml':
        content = generateXMLReport(vulnerabilities);
        filename = `vulnerabilities-${new Date().toISOString().split('T')[0]}.xml`;
        mimeType = 'application/xml';
        break;
    }

    // Create and download file
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    onClose();
  };

  const generateMarkdownReport = (vulns: Vulnerability[]): string => {
    return `# Vulnerability Report

Generated on: ${new Date().toLocaleString()}  
Total Vulnerabilities: ${vulns.length}

## Summary

| Severity | Count |
|----------|-------|
| Critical | ${vulns.filter(v => v.severity === 'critical').length} |
| High | ${vulns.filter(v => v.severity === 'high').length} |
| Medium | ${vulns.filter(v => v.severity === 'medium').length} |
| Low | ${vulns.filter(v => v.severity === 'low').length} |

## Detailed Findings

${vulns.map((vuln, index) => `
### ${index + 1}. ${vuln.title}

**Severity:** ${vuln.severity.toUpperCase()}  
**CVE ID:** ${vuln.cveId || 'N/A'}  
**CVSS Score:** ${vuln.cvssScore || 'N/A'}  
**Published:** ${vuln.publishedDate || 'N/A'}

#### Description
${vuln.description}

#### Impact
${vuln.impact}

#### Recommendation
${vuln.recommendation}

#### References
${vuln.references?.map(ref => `- [${ref.title}](${ref.url})`).join('\n') || 'No references available'}

---
`).join('\n')}

*Report generated by SecureVault Pro*
`;
  };

  const generateXMLReport = (vulns: Vulnerability[]): string => {
    return `<?xml version="1.0" encoding="UTF-8"?>
<vulnerabilities>
  <metadata>
    <generated>${new Date().toISOString()}</generated>
    <count>${vulns.length}</count>
  </metadata>
  <findings>
${vulns.map(vuln => `    <vulnerability>
      <id>${vuln.id}</id>
      <title><![CDATA[${vuln.title}]]></title>
      <severity>${vuln.severity}</severity>
      <cvssScore>${vuln.cvssScore || ''}</cvssScore>
      <cveId>${vuln.cveId || ''}</cveId>
      <publishedDate>${vuln.publishedDate || ''}</publishedDate>
      <description><![CDATA[${vuln.description}]]></description>
      <impact><![CDATA[${vuln.impact}]]></impact>
      <recommendation><![CDATA[${vuln.recommendation}]]></recommendation>
      <references>
${vuln.references?.map(ref => `        <reference>
          <title><![CDATA[${ref.title}]]></title>
          <url>${ref.url}</url>
          <type>${ref.type}</type>
        </reference>`).join('\n') || ''}
      </references>
    </vulnerability>`).join('\n')}
  </findings>
</vulnerabilities>`;
  };

  const formatOptions = [
    {
      value: 'json',
      label: 'JSON',
      description: 'Machine-readable format for APIs and tools',
      icon: Code,
      color: 'text-blue-600'
    },
    {
      value: 'csv',
      label: 'CSV',
      description: 'Spreadsheet format for data analysis',
      icon: Table,
      color: 'text-green-600'
    },
    {
      value: 'markdown',
      label: 'Markdown Report',
      description: 'Human-readable report format',
      icon: FileText,
      color: 'text-purple-600'
    },
    {
      value: 'xml',
      label: 'XML',
      description: 'Structured format for enterprise systems',
      icon: Code,
      color: 'text-orange-600'
    }
  ] as const;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl max-w-2xl w-full">
        {/* Header */}
        <div className="p-6 border-b border-slate-200 dark:border-slate-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Download className="w-6 h-6 text-primary-600" />
              <h2 className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                Export Vulnerabilities
              </h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-slate-600 hover:text-slate-900 dark:text-slate-400 dark:hover:text-slate-100 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          <p className="text-slate-600 dark:text-slate-400 mt-2">
            Export {vulnerabilities.length} vulnerabilities in your preferred format
          </p>
        </div>

        <div className="p-6">
          {/* Format Selection */}
          <div className="space-y-3 mb-6">
            <h3 className="text-lg font-semibold text-slate-900 dark:text-slate-100 mb-4">
              Select Export Format
            </h3>
            {formatOptions.map((option) => {
              const IconComponent = option.icon;
              return (
                <label
                  key={option.value}
                  className={`flex items-center p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    selectedFormat === option.value
                      ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20'
                      : 'border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600'
                  }`}
                >
                  <input
                    type="radio"
                    name="format"
                    value={option.value}
                    checked={selectedFormat === option.value}
                    onChange={(e) => setSelectedFormat(e.target.value as any)}
                    className="sr-only"
                  />
                  <IconComponent className={`w-6 h-6 ${option.color} mr-4`} />
                  <div className="flex-1">
                    <div className="font-medium text-slate-900 dark:text-slate-100">
                      {option.label}
                    </div>
                    <div className="text-sm text-slate-600 dark:text-slate-400">
                      {option.description}
                    </div>
                  </div>
                  {selectedFormat === option.value && (
                    <div className="w-4 h-4 bg-primary-600 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  )}
                </label>
              );
            })}
          </div>

          {/* Preview */}
          <div className="mb-6">
            <h4 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">
              Export Preview
            </h4>
            <div className="bg-slate-100 dark:bg-slate-700 rounded-lg p-3 text-sm font-mono text-slate-700 dark:text-slate-300">
              {selectedFormat === 'json' && '{\n  "vulnerabilities": [\n    {\n      "id": "CVE-2024-0001",\n      "title": "SQL Injection...",\n      ...\n    }\n  ]\n}'}
              {selectedFormat === 'csv' && 'ID,Title,Severity,CVSS Score,CVE ID,...\nCVE-2024-0001,"SQL Injection...",critical,9.8,...'}
              {selectedFormat === 'markdown' && '# Vulnerability Report\n\n## Summary\n\n| Severity | Count |\n|----------|-------|\n| Critical | 1 |\n\n## Detailed Findings\n\n### 1. SQL Injection...'}
              {selectedFormat === 'xml' && '<?xml version="1.0"?>\n<vulnerabilities>\n  <vulnerability>\n    <id>CVE-2024-0001</id>\n    <title>SQL Injection...</title>\n    ...\n  </vulnerability>\n</vulnerabilities>'}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-4">
            <button
              onClick={onClose}
              className="px-6 py-2 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={exportData}
              className="flex items-center space-x-2 px-6 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
            >
              <Download className="w-4 h-4" />
              <span>Export {selectedFormat.toUpperCase()}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExportMenu;
