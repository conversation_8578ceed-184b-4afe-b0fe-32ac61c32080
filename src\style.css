@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* 3D Flip Animation Styles */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* 3D Flip Animation Styles */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

@layer base {
  body {
    @apply bg-gradient-to-br from-slate-50 to-blue-50 dark:from-slate-900 dark:to-slate-800 font-sans antialiased;
    margin: 0;
    min-height: 100vh;
  }
}

@layer components {
  .vulnerability-card {
    @apply bg-white dark:bg-slate-800 rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 p-6 transition-all duration-300 hover:shadow-xl hover:scale-[1.02];
  }

  .search-input {
    @apply w-full px-4 py-3 text-lg border-2 border-slate-300 dark:border-slate-600 rounded-xl focus:border-primary-500 focus:ring-4 focus:ring-primary-100 dark:focus:ring-primary-900 transition-all duration-200 bg-white dark:bg-slate-800 text-slate-900 dark:text-slate-100;
  }

  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-6 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-primary-100;
  }

  .severity-critical {
    @apply bg-danger-100 text-danger-800 border-danger-200;
  }

  .severity-high {
    @apply bg-warning-100 text-warning-800 border-warning-200;
  }

  .severity-medium {
    @apply bg-yellow-100 text-yellow-800 border-yellow-200;
  }

  .severity-low {
    @apply bg-success-100 text-success-800 border-success-200;
  }

  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;
  }

  .card-hover {
    @apply transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] hover:-translate-y-1;
  }

  .fade-in {
    animation: fadeIn 0.6s ease-out forwards;
  }

  .slide-in-up {
    animation: slideInUp 0.4s ease-out forwards;
  }

  .bounce-in {
    animation: bounceIn 0.6s ease-out forwards;
  }

  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
  }
}

/* Responsive Design Improvements */
@media (max-width: 768px) {
  .vulnerability-card {
    @apply p-4;
  }

  .search-input {
    @apply text-base py-4;
  }

  .btn-primary {
    @apply py-4 px-8 text-base;
  }
}

/* Dark mode transitions */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-slate-100 dark:bg-slate-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-slate-400 dark:bg-slate-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-slate-500 dark:bg-slate-500;
}
