# SecureVault Pro Environment Configuration

# Application Settings
VITE_APP_TITLE=SecureVault Pro
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=SecureVault Pro is a comprehensive, enterprise-grade vulnerability research and penetration testing platform specifically engineered for cybersecurity professionals, ethical hackers, security researchers, penetration testers, and enterprise security teams worldwide. This revolutionary platform transforms vulnerability management by integrating real-time vulnerability intelligence from over 15 authoritative sources including the National Vulnerability Database (NVD), Common Vulnerabilities and Exposures (CVE) databases, GitHub Security Advisories, Open Source Vulnerabilities (OSV), ExploitDB, Snyk, Vulners, and numerous other critical security intelligence feeds. Built with cutting-edge modern technologies including React 19, TypeScript 5.0, Tailwind CSS 3.4, and advanced API integration frameworks, the platform provides sophisticated dynamic vulnerability discovery capabilities, AI-powered threat analysis engines, automated risk assessment tools, real-time threat correlation, and professional-grade penetration testing report generation capabilities that support multiple export formats including PDF, Word, Excel, and JSON. The platform features advanced real-time API integration with major vulnerability databases, intelligent multi-source search algorithms, sophisticated customizable filtering systems, automated vulnerability correlation engines, comprehensive threat intelligence feeds, and enterprise-grade report generation tools that ensure compliance with industry standards like ISO 27001, NIST Cybersecurity Framework, and PCI DSS requirements. SecureVault Pro dramatically streamlines the entire vulnerability assessment workflow by providing instant access to the latest global security intelligence, enabling security professionals to efficiently identify, analyze, prioritize, correlate, and document vulnerabilities across their entire infrastructure and application portfolio. With its intuitive user interface, powerful backend services, advanced caching mechanisms, enterprise-grade security features, and comprehensive API ecosystem, it serves as an indispensable tool for conducting thorough security assessments, generating professional penetration testing reports, maintaining up-to-date threat intelligence databases, and ensuring comprehensive security posture management for modern enterprise security operations and cybersecurity programs.

# API Configuration - Real Vulnerability Databases
VITE_NVD_API_URL=https://services.nvd.nist.gov/rest/json/cves/2.0
VITE_CVE_API_URL=https://cve.mitre.org/cgi-bin/cvekey.cgi
VITE_GITHUB_API_URL=https://api.github.com/advisories
VITE_OSV_API_URL=https://api.osv.dev/v1
VITE_CIRCL_API_URL=https://cve.circl.lu/api
VITE_OPENCVE_API_URL=https://www.opencve.io/api
VITE_VULNDB_API_URL=https://vulndb.cyberriskanalytics.com/api/v1
VITE_SNYK_API_URL=https://snyk.io/api/v1
VITE_VULNERS_API_URL=https://vulners.com/api/v3

# AI Integration (Optional - for enhanced analysis)
VITE_OPENAI_API_KEY=your_openai_api_key_here
VITE_ANTHROPIC_API_KEY=your_anthropic_api_key_here

# CORS Proxy for client-side API calls
VITE_CORS_PROXY=https://api.allorigins.win/raw?url=

# Feature Flags
VITE_ENABLE_REAL_TIME_APIS=true
VITE_ENABLE_AI_ANALYSIS=true
VITE_ENABLE_DARK_MODE=true
VITE_ENABLE_EXPORT=true
VITE_ENABLE_REPORTS=true
VITE_ENABLE_THREAT_INTEL=true
VITE_ENABLE_ANALYTICS=false

# Rate Limiting
VITE_API_RATE_LIMIT=100
VITE_CACHE_TIMEOUT=300000

# External Services
VITE_GITHUB_REPO=https://github.com/securevault/securevault-pro
VITE_SUPPORT_EMAIL=<EMAIL>
VITE_DISCORD_INVITE=https://discord.gg/securevault

# Development Settings
VITE_DEV_MODE=true
VITE_DEBUG_LOGS=true
VITE_MOCK_APIS=false
